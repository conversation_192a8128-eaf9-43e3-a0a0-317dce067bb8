/* Global styles for XBot AI */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Open Sans', 'Ubuntu', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Ubuntu', 'Open Sans', 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Focus styles */
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #9c88ff !important;
}

/* Custom button hover effects */
.MuiButton-contained:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(156, 136, 255, 0.3) !important;
}

/* Card hover effects */
.MuiCard-root:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* Loading animation for typing indicator */
@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.typing-dot {
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  
  .MuiCard-root {
    margin-bottom: 12px;
  }
  
  .MuiTypography-h4 {
    font-size: 1.75rem !important;
  }
  
  .MuiTypography-h6 {
    font-size: 1.1rem !important;
  }
}

/* Ensure proper text selection */
.MuiTypography-root {
  user-select: text;
}

/* Custom gradient backgrounds */
.gradient-primary {
  background: linear-gradient(45deg, #9c88ff, #6c5ce7);
}

.gradient-secondary {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

/* Message bubble animations */
.message-bubble {
  animation: fadeIn 0.3s ease-out;
}

/* Rating component styling */
.MuiRating-root {
  color: #ffc107;
}

/* Drawer transition */
.MuiDrawer-paper {
  transition: transform 0.3s ease-in-out !important;
}

/* Custom chip styling */
.MuiChip-root {
  border-radius: 16px;
}

/* Progress bar styling */
.MuiLinearProgress-root {
  border-radius: 4px;
  background-color: rgba(156, 136, 255, 0.1) !important;
}

.MuiLinearProgress-bar {
  background: linear-gradient(45deg, #9c88ff, #6c5ce7) !important;
}

/* Dialog styling */
.MuiDialog-paper {
  border-radius: 12px !important;
}

/* App bar styling */
.MuiAppBar-root {
  background: linear-gradient(45deg, #9c88ff, #6c5ce7) !important;
}

/* Ensure proper spacing */
.MuiListItem-root {
  margin-bottom: 4px;
}

/* Custom shadow for elevated elements */
.elevated-shadow {
  box-shadow: 0 4px 20px rgba(156, 136, 255, 0.15) !important;
}

/* Responsive text */
@media (max-width: 600px) {
  .responsive-text {
    font-size: 0.875rem !important;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease;
}

/* Focus visible for accessibility */
.MuiButton-root:focus-visible,
.MuiIconButton-root:focus-visible,
.MuiListItemButton-root:focus-visible {
  outline: 2px solid #9c88ff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .MuiButton-contained {
    border: 2px solid #000;
  }
  
  .MuiCard-root {
    border: 1px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
